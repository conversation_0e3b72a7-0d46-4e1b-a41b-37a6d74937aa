// 简化的SBE Trade结构体用于测试
#[derive(Debug)]
struct SbeTrade<'a> {
    symbol: &'a str,
    trade_id: u64,
    price: f64,
    quantity: f64,
    event_time: u64,
    trade_time: u64,
    is_buyer_maker: bool,
}

fn parse_sbe_trade_body<'a>(
    message_body: &'a [u8],
    variable_data: &'a [u8],
) -> Option<SbeTrade<'a>> {
    // 跳过前2字节 + 4字节trade_count = 6字节
    let mut offset = 6;

    // 检查是否有足够的数据解析一个trade记录 (25字节)
    let trade_record_size = 25;
    if offset + trade_record_size > variable_data.len() {
        return None;
    }

    // 解析第一个trade记录
    let trade_id = u64::from_le_bytes([
        variable_data[offset],
        variable_data[offset + 1],
        variable_data[offset + 2],
        variable_data[offset + 3],
        variable_data[offset + 4],
        variable_data[offset + 5],
        variable_data[offset + 6],
        variable_data[offset + 7],
    ]);
    offset += 8;

    // 解析price (8字节)
    let price_raw = u64::from_le_bytes([
        variable_data[offset],
        variable_data[offset + 1],
        variable_data[offset + 2],
        variable_data[offset + 3],
        variable_data[offset + 4],
        variable_data[offset + 5],
        variable_data[offset + 6],
        variable_data[offset + 7],
    ]);
    offset += 8;
    // 基于实际数据分析，使用8位小数精度
    let price = price_raw as f64 / 100000000.0;

    // 解析quantity (8字节)
    let quantity_raw = u64::from_le_bytes([
        variable_data[offset],
        variable_data[offset + 1],
        variable_data[offset + 2],
        variable_data[offset + 3],
        variable_data[offset + 4],
        variable_data[offset + 5],
        variable_data[offset + 6],
        variable_data[offset + 7],
    ]);
    offset += 8;
    let quantity = quantity_raw as f64 / 100000000.0;

    // 跳过剩余的1字节（可能是isBuyerMaker或其他标志）
    let is_buyer_maker = if offset < variable_data.len() {
        variable_data[offset] != 0
    } else {
        false
    };
    offset += 1;

    // 解析symbol（在变长数据的末尾）
    // 根据实际数据，symbol在最后，长度为9，内容为"FDUSDUSDT"
    let remaining_data = &variable_data[31..]; // 跳过25字节trade记录 + 6字节前缀
    println!("剩余数据: {:?}", remaining_data);

    if remaining_data.len() < 2 {
        return None;
    }

    let symbol_length = remaining_data[0] as usize;
    println!("Symbol长度: {}", symbol_length);

    if symbol_length == 0 || 1 + symbol_length > remaining_data.len() {
        return None;
    }

    let symbol_bytes = &remaining_data[1..1 + symbol_length];
    let symbol = std::str::from_utf8(symbol_bytes).ok()?;
    println!("解析的symbol: {}", symbol);

    Some(SbeTrade {
        symbol,
        trade_id,
        price,
        quantity,
        event_time: 0, // 从message_body解析
        trade_time: 0, // 从message_body解析
        is_buyer_maker,
    })
}

fn parse_sbe_trade(data: &[u8]) -> Option<SbeTrade> {
    // 首先检查是否有SBE头部
    if data.len() < 8 {
        return None;
    }

    // 解析SBE头部
    let block_length = u16::from_le_bytes([data[0], data[1]]);
    let template_id = u16::from_le_bytes([data[2], data[3]]);

    // 验证这是一个trade消息
    if template_id != 10000 {
        return None;
    }

    // 提取消息体和变长数据
    let message_body = &data[8..8 + block_length as usize];
    let variable_data = &data[8 + block_length as usize..];

    parse_sbe_trade_body(message_body, variable_data)
}

fn main() {
    println!("最终测试：修正后的SBE Trade解析");

    // 真实的Binance SBE数据
    let real_data = vec![
        18, 0, 16, 39, 1, 0, 0, 0, 106, 105, 121, 178, 234, 56, 6, 0, 79, 104, 121, 178, 234, 56,
        6, 0, 248, 248, 25, 0, 1, 0, 0, 0, 85, 194, 168, 17, 0, 0, 0, 0, 144, 94, 242, 5, 0, 0, 0,
        0, 0, 125, 218, 172, 0, 0, 0, 0, 1, 9, 70, 68, 85, 83, 68, 85, 83, 68, 84,
    ];

    match parse_sbe_trade(&real_data) {
        Some(trade) => {
            println!("✅ 解析成功!");
            println!("  Symbol: {}", trade.symbol);
            println!("  Trade ID: {}", trade.trade_id);
            println!("  Price: {:.8}", trade.price);
            println!("  Quantity: {:.8}", trade.quantity);
            println!("  Is Buyer Maker: {}", trade.is_buyer_maker);

            // 验证合理性
            println!("\n=== 合理性检查 ===");
            if trade.price > 0.99 && trade.price < 1.01 {
                println!("✅ 价格合理 (FDUSD稳定币应该接近1.0)");
            } else {
                println!("❌ 价格异常: {}", trade.price);
            }

            if trade.quantity > 0.0 && trade.quantity < 1000000.0 {
                println!("✅ 数量合理");
            } else {
                println!("❌ 数量异常: {}", trade.quantity);
            }
        }
        None => {
            println!("❌ 解析失败");
        }
    }
}
