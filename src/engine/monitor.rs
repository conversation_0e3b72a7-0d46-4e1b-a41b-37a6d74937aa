use crate::{
    EdgeDirection, PREDEFINED_RINGS,
    encoding::order_update::{OrderResponse, parse_order_update},
    engine::trading_pair::EXPECT_PRICE,
    utils::{
        self,
        perf::{circles_to_ns, percentile},
    },
};

#[derive(Debug)]
pub struct LatencyStats {
    test_order_latencies: [f64; 1000],
    order_latencies: [f64; 100],
    test_order_index: usize,
    order_index: usize,
    pub win_count: usize,
    pub loss_count: usize,
}

impl LatencyStats {
    pub fn new() -> Self {
        Self {
            test_order_latencies: [0.0; 1000],
            order_latencies: [0.0; 100],
            test_order_index: 0,
            order_index: 0,
            win_count: 1,
            loss_count: 1,
        }
    }

    #[inline(always)]
    pub fn add_arbitrage_latency(&mut self, latency_ns: f64) {
        if self.order_index % 10 == 0 {
            println!("order latencies");
            for i in 0..self.order_index {
                print!("{} ", self.order_latencies[i] / 1000.0);
            }
            println!();
            self.order_index = 0;
        }
        self.order_latencies[self.order_index] = latency_ns;
        self.order_index += 1;
    }

    #[inline(always)]
    pub fn add_test_latency(&mut self, latency_ns: f64) {
        if self.test_order_index >= 1000 {
            let p50 = percentile(&mut self.test_order_latencies[..self.test_order_index], 0.5);
            println!("Test order latency p50: {:.2}us", p50 / 1000.0);
            self.test_order_index = 0;
        }
        self.test_order_latencies[self.test_order_index] = latency_ns;
        self.test_order_index += 1;
    }

    pub fn print_stats(&self) {
        print!("Wins: {} ", self.win_count - 1);
        println!("Losses: {}", self.loss_count - 1);
    }
}

// 全局延迟统计
static mut LATENCY_STATS: Option<LatencyStats> = None;

pub fn init_latency_stats() {
    unsafe {
        LATENCY_STATS = Some(LatencyStats::new());
    }
}

#[inline(always)]
pub fn add_arbitrage_latency(latency_ns: f64) {
    unsafe {
        // 单线程环境，直接访问，无需任何同步开销
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.add_arbitrage_latency(latency_ns);
        }
    }
}

#[inline(always)]
pub fn add_test_latency(latency_ns: f64) {
    unsafe {
        // 单线程环境，直接访问，无需任何同步开销
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.add_test_latency(latency_ns);
        }
    }
}

pub fn print_latency_stats() {
    unsafe {
        if let Some(ref stats) = LATENCY_STATS {
            stats.print_stats();
        }
    }
}

static mut ORDER_STATUS: [u8; 10] = [0; 10]; // 1 for FILLED, 2 for others
static mut ORDER_FILL_PRICE: [f64; 10] = [0.0; 10];
pub static mut CURRENT_RING_TS: u64 = 0;
pub static mut CURRENT_RING_INDEX: usize = 0;
static mut ORDER_FILL_COUNT: usize = 0;

pub fn monitor_order_execution(edge_index: usize, msg: &[u8]) {
    if let Some(order_res) = parse_order_update(msg) {
        match order_res {
            OrderResponse::OrderUpdate(ou) => {
                let ring_index = ou.order_id.ring_index;
                let ring_len = PREDEFINED_RINGS[ring_index].len();
                let edge_index = ou.order_id.edge_index;
                let order_create_time = ou.order_id.order_create_time;
                let is_testing = ou.order_id.is_testing;
                match ou.status.as_str() {
                    "NEW" => {
                        let now = utils::perf::now();
                        let latency_ns = circles_to_ns(now - order_create_time);
                        if is_testing {
                            add_test_latency(latency_ns);
                        } else {
                            add_arbitrage_latency(latency_ns);
                        }
                    }
                    "REJECTED" | "EXPIRED" | "CANCELED" => {
                        if is_testing {
                            return;
                        }
                        if order_create_time < unsafe { CURRENT_RING_TS } {
                            return;
                        }
                        unsafe {
                            if ORDER_STATUS[edge_index] != 0 {
                                return;
                            }
                            ORDER_STATUS[edge_index] = 2;
                            ORDER_FILL_COUNT += 1;
                            if ORDER_FILL_COUNT == ring_len {
                                ORDER_FILL_COUNT = 0;
                                ORDER_STATUS = [0; 10];
                                if let Some(ref mut stats) = LATENCY_STATS {
                                    stats.loss_count += 1;
                                    stats.print_stats();
                                }
                            }
                        }
                    }
                    "TRADE" => {
                        if is_testing {
                            return;
                        }
                        if order_create_time < unsafe { CURRENT_RING_TS } {
                            return;
                        }
                        // if ou.remaining_quantity > 0.0 {
                        //     return;
                        // }
                        unsafe {
                            if ou.remaining_quantity == 0.0 {
                                ORDER_FILL_COUNT += 1;
                            }
                            match PREDEFINED_RINGS[ring_index][edge_index].1 {
                                EdgeDirection::Forward => {
                                    ORDER_FILL_PRICE[edge_index] = 1.0 / ou.fill_price;
                                }
                                EdgeDirection::Reverse => {
                                    ORDER_FILL_PRICE[edge_index] = ou.fill_price;
                                }
                            }
                            if ORDER_FILL_COUNT == ring_len {
                                ORDER_FILL_COUNT = 0;
                                // let mut expect = 1.0f64;
                                let mut real = 1.0f64;
                                let expect = match ring_len {
                                    3 => 1.0004,
                                    _ => 1.0007,
                                };
                                for i in 0..ring_len {
                                    // expect *= EXPECT_PRICE[i];
                                    println!("{} {}", i, EXPECT_PRICE[i]);
                                    real *= ORDER_FILL_PRICE[i];
                                }
                                // println!("expect: {}, real: {}", expect, real);
                                if (real - expect) < -0.0000001 {
                                    if let Some(ref mut stats) = LATENCY_STATS {
                                        stats.loss_count += 1;
                                        stats.print_stats();
                                    }
                                } else {
                                    if let Some(ref mut stats) = LATENCY_STATS {
                                        stats.win_count += 1;
                                        stats.print_stats();
                                    }
                                }
                                ORDER_STATUS = [0; 10];
                            }
                        }
                    }
                    _ => {}
                }
            }
            OrderResponse::OrderCreated(_ou) => {}
            OrderResponse::OrderError(error_msg) => unsafe {
                // println!(
                //     "Order error: {}, trading pair: {}",
                //     error_msg, PREDEFINED_RINGS[CURRENT_RING_INDEX][edge_index].0
                // );
                CURRENT_RING_TS += 1;
                ORDER_FILL_COUNT = 0;
            },
        }
    } else {
        if memchr::memmem::find(msg, b"outboundAccountPosition").is_some() {
            return;
        }
        // println!(
        //     "Failed to parse order update, {:?}",
        //     String::from_utf8_lossy(msg)
        // );
    }
}
