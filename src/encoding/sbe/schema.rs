use std::mem;

/// SBE消息类型枚举
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq)]
#[repr(u16)]
pub enum SbeMessageType {
    BookTicker = 1001,
    BestBidAsk = 1002, // Binance SBE BestBidAskStreamEvent
    Trade = 10000,
    OrderBookUpdate = 1004,
    Heartbeat = 9999,
    // Binance 实际使用的 template IDs
    BinanceBestBidAsk = 10001, // 实际的 Binance BestBidAsk 消息
}

impl From<u16> for SbeMessageType {
    fn from(value: u16) -> Self {
        match value {
            1001 => SbeMessageType::BookTicker,
            1002 => SbeMessageType::BestBidAsk,
            1003 => SbeMessageType::Trade,  // 备用 Trade Template ID
            10000 => SbeMessageType::Trade, // 真实的 Binance Trade Template ID
            10003 => SbeMessageType::Trade, // 另一个备用 Trade Template ID
            1004 => SbeMessageType::OrderBookUpdate,
            9999 => SbeMessageType::Heartbeat,
            10001 => SbeMessageType::BinanceBestBidAsk,
            _ => panic!("Unknown SBE message type: {}", value),
        }
    }
}

/// SBE消息头部结构 (Binance格式)
#[derive(Debug, Clone, Copy)]
#[repr(C, packed)]
pub struct SbeHeader {
    pub block_length: u16, // 消息块长度
    pub template_id: u16,  // 模板ID（消息类型）
    pub schema_id: u16,    // Schema ID
    pub version: u16,      // Schema版本
}

impl SbeHeader {
    pub const SIZE: usize = mem::size_of::<Self>();

    /// 从字节数组解析SBE头部
    pub fn from_bytes(bytes: &[u8]) -> Option<Self> {
        if bytes.len() < Self::SIZE {
            return None;
        }

        // 安全地解析字段，避免对齐问题
        let block_length = u16::from_le_bytes([bytes[0], bytes[1]]);
        let template_id = u16::from_le_bytes([bytes[2], bytes[3]]);
        let schema_id = u16::from_le_bytes([bytes[4], bytes[5]]);
        let version = u16::from_le_bytes([bytes[6], bytes[7]]);

        Some(Self {
            block_length,
            template_id,
            schema_id,
            version,
        })
    }

    /// 获取消息类型
    pub fn message_type(&self) -> SbeMessageType {
        SbeMessageType::from(self.template_id)
    }

    /// 验证头部有效性
    pub fn is_valid(&self) -> bool {
        // 复制字段值以避免packed struct的对齐问题
        let schema_id = self.schema_id;
        let version = self.version;

        // Binance SBE 格式验证：schema_id == 1, version 可以是 0 或 1
        let schema_valid = schema_id == 1;
        let version_valid = version == 0 || version == 1;

        schema_valid && version_valid
    }
}

/// SBE字段类型定义
pub trait SbeField {
    fn from_bytes(bytes: &[u8], offset: usize) -> Self;
    fn size() -> usize;
}

impl SbeField for u8 {
    fn from_bytes(bytes: &[u8], offset: usize) -> Self {
        bytes[offset]
    }

    fn size() -> usize {
        1
    }
}

impl SbeField for u16 {
    fn from_bytes(bytes: &[u8], offset: usize) -> Self {
        u16::from_le_bytes([bytes[offset], bytes[offset + 1]])
    }

    fn size() -> usize {
        2
    }
}

impl SbeField for u32 {
    fn from_bytes(bytes: &[u8], offset: usize) -> Self {
        u32::from_le_bytes([
            bytes[offset],
            bytes[offset + 1],
            bytes[offset + 2],
            bytes[offset + 3],
        ])
    }

    fn size() -> usize {
        4
    }
}

impl SbeField for u64 {
    fn from_bytes(bytes: &[u8], offset: usize) -> Self {
        // ARM64优化：使用NEON指令加速8字节加载
        #[cfg(target_arch = "aarch64")]
        unsafe {
            use std::arch::aarch64::*;
            let data = vld1_u8(bytes.as_ptr().add(offset));
            u64::from_le_bytes([
                vget_lane_u8(data, 0),
                vget_lane_u8(data, 1),
                vget_lane_u8(data, 2),
                vget_lane_u8(data, 3),
                vget_lane_u8(data, 4),
                vget_lane_u8(data, 5),
                vget_lane_u8(data, 6),
                vget_lane_u8(data, 7),
            ])
        }

        // x86_64优化：使用SSE指令加速8字节加载
        #[cfg(target_arch = "x86_64")]
        unsafe {
            use std::arch::x86_64::*;
            let data = _mm_loadl_epi64(bytes.as_ptr().add(offset) as *const __m128i);
            u64::from_le_bytes([
                _mm_extract_epi8(data, 0) as u8,
                _mm_extract_epi8(data, 1) as u8,
                _mm_extract_epi8(data, 2) as u8,
                _mm_extract_epi8(data, 3) as u8,
                _mm_extract_epi8(data, 4) as u8,
                _mm_extract_epi8(data, 5) as u8,
                _mm_extract_epi8(data, 6) as u8,
                _mm_extract_epi8(data, 7) as u8,
            ])
        }

        // 通用实现
        #[cfg(not(any(target_arch = "x86_64", target_arch = "aarch64")))]
        {
            u64::from_le_bytes([
                bytes[offset],
                bytes[offset + 1],
                bytes[offset + 2],
                bytes[offset + 3],
                bytes[offset + 4],
                bytes[offset + 5],
                bytes[offset + 6],
                bytes[offset + 7],
            ])
        }
    }

    fn size() -> usize {
        8
    }
}

impl SbeField for f64 {
    fn from_bytes(bytes: &[u8], offset: usize) -> Self {
        // ARM64优化：使用NEON指令加速8字节加载
        #[cfg(target_arch = "aarch64")]
        unsafe {
            use std::arch::aarch64::*;
            let data = vld1_u8(bytes.as_ptr().add(offset));
            f64::from_le_bytes([
                vget_lane_u8(data, 0),
                vget_lane_u8(data, 1),
                vget_lane_u8(data, 2),
                vget_lane_u8(data, 3),
                vget_lane_u8(data, 4),
                vget_lane_u8(data, 5),
                vget_lane_u8(data, 6),
                vget_lane_u8(data, 7),
            ])
        }

        // x86_64优化：使用SSE指令加速8字节加载
        #[cfg(target_arch = "x86_64")]
        unsafe {
            use std::arch::x86_64::*;
            let data = _mm_loadl_epi64(bytes.as_ptr().add(offset) as *const __m128i);
            f64::from_le_bytes([
                _mm_extract_epi8(data, 0) as u8,
                _mm_extract_epi8(data, 1) as u8,
                _mm_extract_epi8(data, 2) as u8,
                _mm_extract_epi8(data, 3) as u8,
                _mm_extract_epi8(data, 4) as u8,
                _mm_extract_epi8(data, 5) as u8,
                _mm_extract_epi8(data, 6) as u8,
                _mm_extract_epi8(data, 7) as u8,
            ])
        }

        // 通用实现
        #[cfg(not(any(target_arch = "x86_64", target_arch = "aarch64")))]
        {
            f64::from_le_bytes([
                bytes[offset],
                bytes[offset + 1],
                bytes[offset + 2],
                bytes[offset + 3],
                bytes[offset + 4],
                bytes[offset + 5],
                bytes[offset + 6],
                bytes[offset + 7],
            ])
        }
    }

    fn size() -> usize {
        8
    }
}

/// SBE字符串字段解析
pub fn parse_sbe_string(bytes: &[u8], offset: usize, max_length: usize) -> &str {
    let end = offset + max_length;
    let slice = &bytes[offset..end.min(bytes.len())];

    // 找到第一个null字节或使用整个slice
    let null_pos = slice.iter().position(|&b| b == 0).unwrap_or(slice.len());
    let string_bytes = &slice[..null_pos];

    // 安全地转换为字符串
    unsafe { std::mem::transmute::<&[u8], &str>(string_bytes) }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sbe_header_parsing() {
        let data = [
            0x20, 0x00, // block_length = 32
            0xE9, 0x03, // template_id = 1001 (BookTicker)
            0x01, 0x00, // schema_id = 1
            0x01, 0x00, // version = 1
        ];

        let header = SbeHeader::from_bytes(&data).unwrap();
        // 复制字段值以避免对packed字段的引用
        let block_length = header.block_length;
        let template_id = header.template_id;
        let schema_id = header.schema_id;
        let version = header.version;

        assert_eq!(block_length, 32);
        assert_eq!(template_id, 1001);
        assert_eq!(schema_id, 1);
        assert_eq!(version, 1);
        assert!(header.is_valid());
        assert_eq!(header.message_type(), SbeMessageType::BookTicker);
    }

    #[test]
    fn test_sbe_field_parsing() {
        let data = [0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08];

        assert_eq!(u8::from_bytes(&data, 0), 0x01);
        assert_eq!(u16::from_bytes(&data, 0), 0x0201);
        assert_eq!(u32::from_bytes(&data, 0), 0x04030201);
        assert_eq!(u64::from_bytes(&data, 0), 0x0807060504030201);
    }

    #[test]
    fn test_sbe_string_parsing() {
        let data = b"BTCUSDT\0\0\0\0\0\0\0\0\0";
        let symbol = parse_sbe_string(data, 0, 16);
        assert_eq!(symbol, "BTCUSDT");
    }
}
