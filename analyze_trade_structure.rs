fn main() {
    println!("重新分析Trade数据结构，寻找exponent字段...");
    
    // 真实的Binance SBE数据
    let real_data = vec![
        18, 0, 16, 39, 1, 0, 0, 0, 106, 105, 121, 178, 234, 56, 6, 0, 79, 104, 121, 178, 234, 56, 6, 0, 248, 248, 25, 0, 1, 0, 0, 0, 85, 194, 168, 17, 0, 0, 0, 0, 144, 94, 242, 5, 0, 0, 0, 0, 0, 125, 218, 172, 0, 0, 0, 0, 1, 9, 70, 68, 85, 83, 68, 85, 83, 68, 84
    ];

    // 跳过SBE头部(8字节) + 固定字段(18字节) = 26字节
    let variable_data = &real_data[26..];
    
    println!("变长数据: {:?}", variable_data);
    println!("变长数据长度: {}", variable_data.len());
    
    // 跳过前2字节 + 4字节trade_count = 6字节
    let mut offset = 6;
    
    println!("\n=== 按照mantissa+exponent格式解析 ===");
    
    // Trade ID (8字节)
    let trade_id = u64::from_le_bytes([
        variable_data[offset], variable_data[offset + 1], variable_data[offset + 2], variable_data[offset + 3],
        variable_data[offset + 4], variable_data[offset + 5], variable_data[offset + 6], variable_data[offset + 7],
    ]);
    offset += 8;
    println!("Trade ID: {}", trade_id);
    
    // Price mantissa (8字节)
    let price_mantissa = i64::from_le_bytes([
        variable_data[offset], variable_data[offset + 1], variable_data[offset + 2], variable_data[offset + 3],
        variable_data[offset + 4], variable_data[offset + 5], variable_data[offset + 6], variable_data[offset + 7],
    ]);
    offset += 8;
    println!("Price mantissa: {}", price_mantissa);
    
    // Price exponent (1字节)
    if offset < variable_data.len() {
        let price_exponent = variable_data[offset] as i8;
        offset += 1;
        println!("Price exponent: {}", price_exponent);
        
        // 计算实际价格
        let price = if price_exponent < 0 {
            price_mantissa as f64 / (10.0_f64.powi((-price_exponent) as i32))
        } else {
            price_mantissa as f64 * (10.0_f64.powi(price_exponent as i32))
        };
        println!("计算的价格: {}", price);
    }
    
    // Quantity mantissa (8字节)
    if offset + 8 <= variable_data.len() {
        let quantity_mantissa = i64::from_le_bytes([
            variable_data[offset], variable_data[offset + 1], variable_data[offset + 2], variable_data[offset + 3],
            variable_data[offset + 4], variable_data[offset + 5], variable_data[offset + 6], variable_data[offset + 7],
        ]);
        offset += 8;
        println!("Quantity mantissa: {}", quantity_mantissa);
        
        // Quantity exponent (1字节)
        if offset < variable_data.len() {
            let quantity_exponent = variable_data[offset] as i8;
            offset += 1;
            println!("Quantity exponent: {}", quantity_exponent);
            
            // 计算实际数量
            let quantity = if quantity_exponent < 0 {
                quantity_mantissa as f64 / (10.0_f64.powi((-quantity_exponent) as i32))
            } else {
                quantity_mantissa as f64 * (10.0_f64.powi(quantity_exponent as i32))
            };
            println!("计算的数量: {}", quantity);
        }
    }
    
    println!("\n=== 剩余数据分析 ===");
    if offset < variable_data.len() {
        println!("剩余数据 (从offset {}): {:?}", offset, &variable_data[offset..]);
        
        // 分析剩余字节
        for (i, &byte) in variable_data[offset..].iter().enumerate() {
            println!("  [{}] 0x{:02x} ({})", offset + i, byte, byte);
        }
    }
    
    println!("\n=== 完整数据结构分析 ===");
    println!("如果按照25字节trade记录格式:");
    println!("  Trade ID: 8字节");
    println!("  Price: 8字节 mantissa + 1字节 exponent = 9字节");
    println!("  Quantity: 8字节 mantissa + 1字节 exponent = 9字节");
    println!("  总计: 8 + 9 + 9 = 26字节 (不是25字节!)");
    println!("  或者可能有其他字段...");
}
