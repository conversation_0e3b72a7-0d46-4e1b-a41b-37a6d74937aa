{"cells": [{"cell_type": "code", "execution_count": 33, "id": "d7736279", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# pd.options.display.datetime_format = \"%Y-%m-%d %H:%M:%S.%f\"\n", "pd.options.display.precision = 6          # 浮点同时保留 6 位小数\n", "pd.set_option('display.max_rows', None)      # 行数不限\n", "pd.set_option('display.max_columns', None)   # 列数不限\n", "\n", "# 宽度相关\n", "pd.set_option('display.width', None)         # 根据终端自动换行\n", "pd.set_option('display.max_colwidth', None)  # 列内容（字符串）长度不限\n", "\n", "# partiusdt= pd.read_parquet(\"../data/data/partiusdt_trades_20250701_121910.parquet\")\n", "# partiusdc = pd.read_parquet(\"../data/data/partiusdc_trades_20250701_121910.parquet\")\n", "# bmtusdt = pd.read_parquet(\"../data/data/bmtusdt_trades_20250701_121910.parquet\")\n", "trade = pd.read_parquet(\"../data/data/syrupusdc_trades_20250701_121910.parquet\")\n", "bbo = pd.read_parquet(\"../data/data/syrupusdc_bbo_20250701_121910.parquet\")"]}, {"cell_type": "code", "execution_count": 34, "id": "fe1ff0e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                   timestamp     symbol  trade_id   price  quantity  \\\n", "1009 2025-07-01 12:09:58.696  SYRUPUSDC   2696707  0.5258      10.5   \n", "1010 2025-07-01 12:09:58.696  SYRUPUSDC   2696708  0.5259      10.5   \n", "1011 2025-07-01 12:09:58.696  SYRUPUSDC   2696709  0.5259    1758.9   \n", "1012 2025-07-01 12:09:58.696  SYRUPUSDC   2696710  0.5259     109.6   \n", "1013 2025-07-01 12:09:58.696  SYRUPUSDC   2696711  0.5260      10.5   \n", "1014 2025-07-01 12:09:58.697  SYRUPUSDC   2696712  0.5261      10.5   \n", "1015 2025-07-01 12:09:58.697  SYRUPUSDC   2696713  0.5262      10.5   \n", "1016 2025-07-01 12:09:58.698  SYRUPUSDC   2696714  0.5263      10.5   \n", "1017 2025-07-01 12:09:58.698  SYRUPUSDC   2696715  0.5264       8.0   \n", "1018 2025-07-01 12:09:58.699  SYRUPUSDC   2696716  0.5262      95.0   \n", "1019 2025-07-01 12:09:58.699  SYRUPUSDC   2696717  0.5262      38.0   \n", "1020 2025-07-01 12:09:58.699  SYRUPUSDC   2696718  0.5262      12.0   \n", "1021 2025-07-01 12:09:58.705  SYRUPUSDC   2696719  0.5264       2.5   \n", "1022 2025-07-01 12:09:58.705  SYRUPUSDC   2696720  0.5264       9.9   \n", "1023 2025-07-01 12:09:58.714  SYRUPUSDC   2696721  0.5264       9.9   \n", "1024 2025-07-01 12:09:58.758  SYRUPUSDC   2696722  0.5265      10.5   \n", "1025 2025-07-01 12:09:58.833  SYRUPUSDC   2696723  0.5266      10.5   \n", "1026 2025-07-01 12:09:58.834  SYRUPUSDC   2696724  0.5267      10.5   \n", "\n", "     buyer_order_id seller_order_id              trade_time  is_buyer_maker  \n", "1009           None            None 2025-07-01 12:09:58.696           False  \n", "1010           None            None 2025-07-01 12:09:58.696           False  \n", "1011           None            None 2025-07-01 12:09:58.696           False  \n", "1012           None            None 2025-07-01 12:09:58.696           False  \n", "1013           None            None 2025-07-01 12:09:58.696           False  \n", "1014           None            None 2025-07-01 12:09:58.697           False  \n", "1015           None            None 2025-07-01 12:09:58.697           False  \n", "1016           None            None 2025-07-01 12:09:58.698           False  \n", "1017           None            None 2025-07-01 12:09:58.698           False  \n", "1018           None            None 2025-07-01 12:09:58.699            True  \n", "1019           None            None 2025-07-01 12:09:58.699            True  \n", "1020           None            None 2025-07-01 12:09:58.699            True  \n", "1021           None            None 2025-07-01 12:09:58.705           False  \n", "1022           None            None 2025-07-01 12:09:58.705           False  \n", "1023           None            None 2025-07-01 12:09:58.714           False  \n", "1024           None            None 2025-07-01 12:09:58.758           False  \n", "1025           None            None 2025-07-01 12:09:58.833           False  \n", "1026           None            None 2025-07-01 12:09:58.834           False  \n", "                   timestamp     symbol  bid_price  bid_qty  ask_price  \\\n", "8583 2025-07-01 12:09:57.623  SYRUPUSDC     0.5257   3540.8     0.5258   \n", "8584 2025-07-01 12:09:58.698  SYRUPUSDC     0.5257   3540.8     0.5259   \n", "8585 2025-07-01 12:09:58.698  SYRUPUSDC     0.5257   3540.8     0.5259   \n", "8586 2025-07-01 12:09:58.698  SYRUPUSDC     0.5258    149.7     0.5259   \n", "8587 2025-07-01 12:09:58.698  SYRUPUSDC     0.5258    149.7     0.5260   \n", "8588 2025-07-01 12:09:58.698  SYRUPUSDC     0.5258    149.7     0.5261   \n", "8589 2025-07-01 12:09:58.698  SYRUPUSDC     0.5258    149.7     0.5262   \n", "8590 2025-07-01 12:09:58.698  SYRUPUSDC     0.5261    149.7     0.5262   \n", "8591 2025-07-01 12:09:58.698  SYRUPUSDC     0.5261    149.7     0.5263   \n", "8592 2025-07-01 12:09:58.699  SYRUPUSDC     0.5261    149.7     0.5264   \n", "8593 2025-07-01 12:09:58.699  SYRUPUSDC     0.5261    220.9     0.5264   \n", "8594 2025-07-01 12:09:58.700  SYRUPUSDC     0.5261    291.8     0.5264   \n", "8595 2025-07-01 12:09:58.700  SYRUPUSDC     0.5261    363.0     0.5264   \n", "8596 2025-07-01 12:09:58.700  SYRUPUSDC     0.5261    434.2     0.5264   \n", "8597 2025-07-01 12:09:58.700  SYRUPUSDC     0.5261    363.0     0.5264   \n", "8598 2025-07-01 12:09:58.700  SYRUPUSDC     0.5262     99.8     0.5264   \n", "8599 2025-07-01 12:09:58.700  SYRUPUSDC     0.5262    392.0     0.5264   \n", "8600 2025-07-01 12:09:58.700  SYRUPUSDC     0.5262     99.8     0.5264   \n", "8601 2025-07-01 12:09:58.700  SYRUPUSDC     0.5262    392.0     0.5264   \n", "8602 2025-07-01 12:09:58.700  SYRUPUSDC     0.5263    149.7     0.5264   \n", "8603 2025-07-01 12:09:58.700  SYRUPUSDC     0.5262    292.2     0.5264   \n", "8604 2025-07-01 12:09:58.700  SYRUPUSDC     0.5262    197.2     0.5264   \n", "8605 2025-07-01 12:09:58.701  SYRUPUSDC     0.5262    159.2     0.5264   \n", "8606 2025-07-01 12:09:58.701  SYRUPUSDC     0.5262    147.2     0.5264   \n", "8607 2025-07-01 12:09:58.701  SYRUPUSDC     0.5261    292.2     0.5264   \n", "8608 2025-07-01 12:09:58.702  SYRUPUSDC     0.5261    292.2     0.5264   \n", "8609 2025-07-01 12:09:58.702  SYRUPUSDC     0.5261    292.2     0.5264   \n", "8610 2025-07-01 12:09:58.703  SYRUPUSDC     0.5262    259.7     0.5264   \n", "8611 2025-07-01 12:09:58.703  SYRUPUSDC     0.5263    292.2     0.5264   \n", "8612 2025-07-01 12:09:58.705  SYRUPUSDC     0.5262    640.1     0.5264   \n", "8613 2025-07-01 12:09:58.706  SYRUPUSDC     0.5263    292.2     0.5264   \n", "8614 2025-07-01 12:09:58.706  SYRUPUSDC     0.5264    133.7     0.5265   \n", "8615 2025-07-01 12:09:58.707  SYRUPUSDC     0.5263    292.2     0.5265   \n", "8616 2025-07-01 12:09:58.711  SYRUPUSDC     0.5263    292.2     0.5264   \n", "8617 2025-07-01 12:09:58.714  SYRUPUSDC     0.5262    640.1     0.5264   \n", "8618 2025-07-01 12:09:58.714  SYRUPUSDC     0.5262    259.7     0.5264   \n", "8619 2025-07-01 12:09:58.715  SYRUPUSDC     0.5263    292.2     0.5264   \n", "8620 2025-07-01 12:09:58.715  SYRUPUSDC     0.5264    136.2     0.5265   \n", "8621 2025-07-01 12:09:58.717  SYRUPUSDC     0.5263    292.2     0.5265   \n", "8622 2025-07-01 12:09:58.759  SYRUPUSDC     0.5264    292.2     0.5265   \n", "8623 2025-07-01 12:09:58.759  SYRUPUSDC     0.5265    135.6     0.5266   \n", "8624 2025-07-01 12:09:58.761  SYRUPUSDC     0.5265    135.6     0.5266   \n", "8625 2025-07-01 12:09:58.761  SYRUPUSDC     0.5265    135.6     0.5266   \n", "8626 2025-07-01 12:09:58.764  SYRUPUSDC     0.5265    292.2     0.5266   \n", "8627 2025-07-01 12:09:58.835  SYRUPUSDC     0.5265    292.2     0.5267   \n", "8628 2025-07-01 12:09:58.835  SYRUPUSDC     0.5265    292.2     0.5268   \n", "8629 2025-07-01 12:09:58.836  SYRUPUSDC     0.5266    190.9     0.5268   \n", "8630 2025-07-01 12:09:58.836  SYRUPUSDC     0.5267    134.1     0.5268   \n", "8631 2025-07-01 12:09:58.836  SYRUPUSDC     0.5267    268.0     0.5268   \n", "8632 2025-07-01 12:09:58.836  SYRUPUSDC     0.5267    402.1     0.5268   \n", "8633 2025-07-01 12:09:58.837  SYRUPUSDC     0.5267    536.2     0.5268   \n", "8634 2025-07-01 12:09:58.837  SYRUPUSDC     0.5267    670.3     0.5268   \n", "8635 2025-07-01 12:09:58.936  SYRUPUSDC     0.5267    536.2     0.5268   \n", "8636 2025-07-01 12:09:58.936  SYRUPUSDC     0.5267    402.1     0.5268   \n", "8637 2025-07-01 12:09:58.936  SYRUPUSDC     0.5267    268.0     0.5268   \n", "8638 2025-07-01 12:09:58.936  SYRUPUSDC     0.5267    134.1     0.5268   \n", "8639 2025-07-01 12:09:58.936  SYRUPUSDC     0.5266    190.9     0.5268   \n", "8640 2025-07-01 12:09:58.937  SYRUPUSDC     0.5266    190.9     0.5268   \n", "8641 2025-07-01 12:09:58.937  SYRUPUSDC     0.5265    292.2     0.5268   \n", "8642 2025-07-01 12:09:58.937  SYRUPUSDC     0.5265    292.2     0.5268   \n", "8643 2025-07-01 12:09:58.937  SYRUPUSDC     0.5265    292.2     0.5267   \n", "8644 2025-07-01 12:09:58.941  SYRUPUSDC     0.5265    292.2     0.5266   \n", "8645 2025-07-01 12:09:58.944  SYRUPUSDC     0.5265    156.6     0.5266   \n", "8646 2025-07-01 12:09:58.944  SYRUPUSDC     0.5264    292.2     0.5266   \n", "8647 2025-07-01 12:09:58.944  SYRUPUSDC     0.5264    292.2     0.5266   \n", "8648 2025-07-01 12:09:58.945  SYRUPUSDC     0.5264    292.2     0.5265   \n", "8649 2025-07-01 12:09:58.945  SYRUPUSDC     0.5264    292.2     0.5265   \n", "8650 2025-07-01 12:09:58.945  SYRUPUSDC     0.5264    292.2     0.5265   \n", "8651 2025-07-01 12:09:58.949  SYRUPUSDC     0.5263    483.8     0.5265   \n", "8652 2025-07-01 12:09:58.949  SYRUPUSDC     0.5263    483.8     0.5265   \n", "8653 2025-07-01 12:09:58.950  SYRUPUSDC     0.5263    483.8     0.5265   \n", "8654 2025-07-01 12:09:58.952  SYRUPUSDC     0.5263    483.8     0.5268   \n", "8655 2025-07-01 12:09:58.957  SYRUPUSDC     0.5264    261.7     0.5268   \n", "8656 2025-07-01 12:09:58.957  SYRUPUSDC     0.5264    261.7     0.5268   \n", "8657 2025-07-01 12:09:58.959  SYRUPUSDC     0.5264    261.7     0.5267   \n", "8658 2025-07-01 12:09:58.962  SYRUPUSDC     0.5264    261.7     0.5268   \n", "8659 2025-07-01 12:09:58.981  SYRUPUSDC     0.5264    261.7     0.5269   \n", "8660 2025-07-01 12:09:58.983  SYRUPUSDC     0.5264    261.7     0.5268   \n", "\n", "      ask_qty  update_id  \n", "8583     10.5   90086136  \n", "8584   1879.0   90086143  \n", "8585    109.6   90086147  \n", "8586    109.6   90086148  \n", "8587     10.5   90086149  \n", "8588     10.5   90086154  \n", "8589     10.5   90086155  \n", "8590     10.5   90086156  \n", "8591     10.5   90086157  \n", "8592     12.4   90086164  \n", "8593     12.4   90086172  \n", "8594     12.4   90086173  \n", "8595     12.4   90086175  \n", "8596     12.4   90086179  \n", "8597     12.4   90086185  \n", "8598     12.4   90086186  \n", "8599     12.4   90086187  \n", "8600     12.4   90086196  \n", "8601     12.4   90086197  \n", "8602     12.4   90086200  \n", "8603     12.4   90086204  \n", "8604     12.4   90086206  \n", "8605     12.4   90086207  \n", "8606     12.4   90086208  \n", "8607     12.4   90086213  \n", "8608    109.7   90086218  \n", "8609     12.4   90086221  \n", "8610     12.4   90086224  \n", "8611     12.4   90086231  \n", "8612     12.4   90086237  \n", "8613     12.4   90086241  \n", "8614     10.5   90086242  \n", "8615     10.5   90086246  \n", "8616      9.9   90086249  \n", "8617      9.9   90086250  \n", "8618      9.9   90086251  \n", "8619      9.9   90086255  \n", "8620     10.5   90086256  \n", "8621     10.5   90086257  \n", "8622     10.5   90086275  \n", "8623     10.5   90086276  \n", "8624    107.8   90086283  \n", "8625     10.5   90086284  \n", "8626     10.5   90086288  \n", "8627     10.5   90086329  \n", "8628     10.5   90086332  \n", "8629     10.5   90086339  \n", "8630     10.5   90086340  \n", "8631     10.5   90086341  \n", "8632     10.5   90086345  \n", "8633     10.5   90086346  \n", "8634     10.5   90086347  \n", "8635     10.5   90086370  \n", "8636     10.5   90086371  \n", "8637     10.5   90086372  \n", "8638     10.5   90086373  \n", "8639     10.5   90086374  \n", "8640    107.8   90086376  \n", "8641    107.8   90086377  \n", "8642    207.6   90086380  \n", "8643    145.9   90086381  \n", "8644     97.3   90086385  \n", "8645     97.3   90086389  \n", "8646     97.3   90086390  \n", "8647    197.1   90086393  \n", "8648    145.9   90086394  \n", "8649    295.6   90086395  \n", "8650    145.9   90086397  \n", "8651    145.9   90086403  \n", "8652    245.7   90086405  \n", "8653    145.9   90086406  \n", "8654    223.8   90086408  \n", "8655    223.8   90086409  \n", "8656     10.5   90086410  \n", "8657     97.3   90086411  \n", "8658     10.5   90086413  \n", "8659    159.9   90086420  \n", "8660     97.3   90086423  \n"]}], "source": ["# partiusdt = partiusdt[(partiusdt['timestamp'] > \"2025-07-01 20:15:05.000\") & (partiusdt['timestamp'] < \"2025-07-01 20:15:06.000\")]\n", "trade = trade[(trade['timestamp'] > \"2025-07-01 12:09:55.000\") & (trade['timestamp'] < \"2025-07-01 12:09:59.000\")]\n", "print(trade)\n", "bbo = bbo[(bbo['timestamp'] > \"2025-07-01 12:09:55.000\") & (bbo['timestamp'] < \"2025-07-01 12:09:59.000\")]\n", "print(bbo)\n", "# bmtusdt = bmtusdt[(bmtusdt['timestamp'] > \"2025-07-01 12:15:05.000\") & (bmtusdt['timestamp'] < \"2025-07-01 12:17:36.000\")]\n", "# bmtusdc = bmtusdc[(bmtusdc['timestamp'] > \"2025-07-01 12:15:35.000\") & (bmtusdc['timestamp'] < \"2025-07-01 12:17:36.000\")]\n", "\n", "# print(bmtusdt)\n", "# print(bmtusdc)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}